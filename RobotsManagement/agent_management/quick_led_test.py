#!/usr/bin/env python3
"""
快速LED测试脚本 - 在agent_management目录中使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agent_management import mqtt_client_thread, ros_node_thread
import socket
import time

def test_led():
    """测试LED功能"""
    
    # MQTT配置
    broker = '*********'
    port = 1883
    keepalive = 60
    
    print("正在连接MQTT...")
    
    # 创建MQTT客户端
    mqtt_client_instance = mqtt_client_thread(
        broker=broker, 
        port=port, 
        keepalive=keepalive, 
        client_id=f'{socket.gethostname()}_led_test'
    )
    
    # 创建ROS节点
    ros_instance = ros_node_thread()
    
    print("连接成功！开始LED测试...")
    
    # 获取在线设备
    agent_list = mqtt_client_instance.get_agent()
    print(f"在线设备: {agent_list}")
    
    if not agent_list:
        print("没有找到在线设备！")
        return
    
    # 测试序列
    test_colors = [
        (0xFF0000, "红色"),
        (0x00FF00, "绿色"), 
        (0x0000FF, "蓝色"),
        (0xFFFF00, "黄色"),
        (0xFF00FF, "紫色"),
        (0x00FFFF, "青色"),
        (0xFFFFFF, "白色"),
        (0x000000, "关闭")
    ]
    
    print("\n开始LED颜色测试...")
    
    for color, color_name in test_colors:
        print(f"设置LED为{color_name} (0x{color:06X})")
        
        # 设置上灯带
        ros_instance.set_led_show(0, 0, color, color, color)
        # 设置下灯带  
        ros_instance.set_led_show(0, 1, color, color, color)
        
        time.sleep(2)  # 等待2秒
    
    print("LED测试完成！")

if __name__ == "__main__":
    test_led()
