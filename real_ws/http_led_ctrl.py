#!/usr/bin/env python3
"""
通过HTTP请求直接控制机器人LED的脚本
绕过MQTT，使用机器人的HTTP API接口
"""

import requests
import json
import time

class HTTPLEDController:
    def __init__(self, robot_ip, robot_port=5000, colors=None, counts=None):
        """
        初始化HTTP LED控制器
        
        Args:
            robot_ip: 机器人的IP地址
            robot_port: 机器人HTTP服务端口
            colors: 颜色列表 (例如: [0xFF0000, 0x000000])
            counts: 每种颜色的显示次数 (例如: [12, 6])
        """
        self.robot_ip = robot_ip
        self.robot_port = robot_port
        self.base_url = f"http://{robot_ip}:{robot_port}"
        self.colors = colors or [0xFF0000, 0x000000]
        self.counts = counts or [12, 6]
        self.current_color_idx = 0
        self.current_count = 0
        
        # 测试连接
        self.test_connection()

    def test_connection(self):
        """测试与机器人的连接"""
        try:
            response = requests.get(f"{self.base_url}/status", timeout=5)
            if response.status_code == 200:
                print(f"Successfully connected to robot at {self.robot_ip}:{self.robot_port}")
            else:
                print(f"Connected but got status code: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"Warning: Could not connect to {self.base_url}")
            print(f"Error: {e}")
            print("Will attempt to send commands anyway...")

    def send_led_command(self, cmd_type, color, num_leds):
        """
        发送LED控制命令到机器人
        
        Args:
            cmd_type: 'ledup' 或 'leddown'
            color: RGB颜色值
            num_leds: LED数量
        """
        # 构造命令消息（与MQTT版本相同的格式）
        cmd_msg = {
            "cmd_type": cmd_type,
            "args_length": 6,
            "args": {
                "0": color,
                "1": num_leds,
                "2": color,
                "3": num_leds,
                "4": color,
                "5": num_leds,
            }
        }
        
        try:
            # 发送HTTP POST请求
            response = requests.post(
                f"{self.base_url}/led_control",
                json=cmd_msg,
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"✓ {cmd_type} command sent successfully: {hex(color)}")
                return True
            else:
                print(f"✗ Failed to send {cmd_type} command: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"✗ HTTP request failed: {e}")
            return False

    def update_color(self):
        """更新当前颜色"""
        self.current_count += 1
        
        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

    def publish_led_color(self, color):
        """
        发布LED颜色到机器人
        
        Args:
            color: RGB颜色值
        """
        # 发送上灯带命令
        self.send_led_command("ledup", color, 14)
        
        # 发送下灯带命令
        self.send_led_command("leddown", color, 30)

    def run_led_sequence(self, total_cycles=5, frequency=12):
        """
        运行LED闪烁序列
        
        Args:
            total_cycles: 总循环次数
            frequency: 频率 (Hz)
        """
        interval = 1.0 / frequency
        total_count = total_cycles * sum(self.counts) - 1
        
        print(f"Starting LED sequence: {total_cycles} cycles at {frequency}Hz")
        print(f"Colors: {[hex(c) for c in self.colors]}")
        print(f"Counts: {self.counts}")
        print(f"Target URL: {self.base_url}")
        
        for count in range(total_count, -1, -1):
            # 更新并发布当前颜色
            current_color = self.colors[self.current_color_idx]
            self.publish_led_color(current_color)
            self.update_color()
            
            print(f"Count: {count}, Color: {hex(current_color)}")
            time.sleep(interval)
        
        # 序列结束后关闭LED
        self.publish_led_color(0x000000)
        print("LED sequence completed, lights turned off")


class SimpleHTTPLEDController:
    """简化版HTTP控制器，使用更简单的API"""
    
    def __init__(self, robot_ip, robot_port=8080):
        self.robot_ip = robot_ip
        self.robot_port = robot_port
        self.base_url = f"http://{robot_ip}:{robot_port}"
    
    def set_led_color(self, color, led_type="both"):
        """
        设置LED颜色（简化版API）
        
        Args:
            color: RGB颜色值 (例如: 0xFF0000)
            led_type: "up", "down", 或 "both"
        """
        try:
            # 尝试简化的API端点
            params = {
                "color": hex(color),
                "type": led_type
            }
            
            response = requests.get(
                f"{self.base_url}/set_led",
                params=params,
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"✓ LED color set to {hex(color)}")
                return True
            else:
                print(f"✗ Failed to set LED color: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"✗ HTTP request failed: {e}")
            return False


def main():
    """主函数"""
    # 配置参数 - 请根据你的机器人实际情况修改
    ROBOT_IP = "*************"  # 替换为你的机器人IP地址
    ROBOT_PORT = 5000  # 机器人HTTP服务端口，可能需要调整
    
    COLORS = [0xFF0000, 0x000000]  # 红色和黑色（关闭）
    COUNTS = [12, 6]  # 红色12次，黑色6次
    CYCLES = 5  # 总共5个循环
    FREQUENCY = 12  # 12Hz频率
    
    print("尝试HTTP LED控制...")
    print(f"目标机器人: {ROBOT_IP}:{ROBOT_PORT}")
    
    try:
        # 尝试完整版HTTP控制器
        controller = HTTPLEDController(ROBOT_IP, ROBOT_PORT, COLORS, COUNTS)
        controller.run_led_sequence(CYCLES, FREQUENCY)
        
    except Exception as e:
        print(f"完整版HTTP控制失败: {e}")
        print("\n尝试简化版HTTP控制...")
        
        try:
            # 尝试简化版控制器
            simple_controller = SimpleHTTPLEDController(ROBOT_IP, 8080)
            
            # 简单的红色闪烁测试
            for i in range(10):
                simple_controller.set_led_color(0xFF0000)  # 红色
                time.sleep(0.5)
                simple_controller.set_led_color(0x000000)  # 关闭
                time.sleep(0.5)
                
        except Exception as e2:
            print(f"简化版HTTP控制也失败: {e2}")
            print("\n建议:")
            print("1. 检查机器人IP地址是否正确")
            print("2. 确认机器人的HTTP服务端口")
            print("3. 检查网络连接")
            print("4. 或者使用ROS话题版本 (direct_ros_led_ctrl.py)")


if __name__ == "__main__":
    main()
