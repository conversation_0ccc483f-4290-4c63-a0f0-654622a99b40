#!/usr/bin/env python3
"""
直接通过ROS话题控制机器人LED的脚本
绕过MQTT，直接使用机器人的IP地址和ROS话题
"""

import rospy
import time
from std_msgs.msg import UInt32MultiArray

class DirectROSLEDController:
    def __init__(self, robot_ip, colors, counts):
        """
        初始化直接ROS LED控制器
        
        Args:
            robot_ip: 机器人的IP地址
            colors: 颜色列表 (例如: [0xFF0000, 0x000000])
            counts: 每种颜色的显示次数 (例如: [12, 6])
        """
        self.robot_ip = robot_ip
        self.colors = colors
        self.counts = counts
        self.current_color_idx = 0
        self.current_count = 0
        
        # 设置ROS Master URI为机器人的IP
        import os
        os.environ['ROS_MASTER_URI'] = f'http://{robot_ip}:11311'
        
        # 初始化ROS节点
        rospy.init_node('direct_led_controller', anonymous=True)
        
        # 创建LED控制发布者
        self.ledup_pub = rospy.Publisher('/robot/ledup', UInt32MultiArray, queue_size=10)
        self.leddown_pub = rospy.Publisher('/robot/leddown', UInt32MultiArray, queue_size=10)
        
        # 等待发布者连接
        time.sleep(1)
        print(f"Connected to robot at {robot_ip}")

    def rgb_to_uint32(self, color, num_leds):
        """
        将RGB颜色转换为机器人需要的uint32格式
        格式: 24位BRG（颜色）+ 8位（数量）
        
        Args:
            color: RGB颜色值 (例如: 0xFF0000 为红色)
            num_leds: LED数量
            
        Returns:
            uint32格式的颜色数据
        """
        r = (color >> 16) & 0xFF
        g = (color >> 8) & 0xFF  
        b = color & 0xFF
        
        # 机器人使用BRG格式，不是RGB
        return (num_leds << 24) | (g << 16) | (r << 8) | b

    def update_color(self):
        """更新当前颜色"""
        self.current_count += 1
        
        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

    def publish_led_color(self, color):
        """
        发布LED颜色到机器人
        
        Args:
            color: RGB颜色值
        """
        # 创建上灯带消息
        ledup_msg = UInt32MultiArray()
        ledup_data = self.rgb_to_uint32(color, 14)  # 上灯带14个LED
        ledup_msg.data = [ledup_data]
        
        # 创建下灯带消息  
        leddown_msg = UInt32MultiArray()
        leddown_data = self.rgb_to_uint32(color, 30)  # 下灯带30个LED
        leddown_msg.data = [leddown_data]
        
        # 发布消息
        self.ledup_pub.publish(ledup_msg)
        self.leddown_pub.publish(leddown_msg)
        
        print(f"Published LED color: {hex(color)}")

    def run_led_sequence(self, total_cycles=5, frequency=12):
        """
        运行LED闪烁序列
        
        Args:
            total_cycles: 总循环次数
            frequency: 频率 (Hz)
        """
        rate = rospy.Rate(frequency)
        total_count = total_cycles * sum(self.counts) - 1
        
        print(f"Starting LED sequence: {total_cycles} cycles at {frequency}Hz")
        print(f"Colors: {[hex(c) for c in self.colors]}")
        print(f"Counts: {self.counts}")
        
        for count in range(total_count, -1, -1):
            if rospy.is_shutdown():
                break
                
            # 更新并发布当前颜色
            current_color = self.colors[self.current_color_idx]
            self.publish_led_color(current_color)
            self.update_color()
            
            print(f"Count: {count}, Color: {hex(current_color)}")
            rate.sleep()
        
        # 序列结束后关闭LED
        self.publish_led_color(0x000000)
        print("LED sequence completed, lights turned off")


def main():
    """主函数"""
    # 配置参数
    ROBOT_IP = "**************"  # 替换为你的机器人IP地址
    COLORS = [0xFF0000, 0x000000]  # 红色和黑色（关闭）
    COUNTS = [12, 6]  # 红色12次，黑色6次
    CYCLES = 5  # 总共5个循环
    FREQUENCY = 12  # 12Hz频率
    
    try:
        # 创建控制器
        controller = DirectROSLEDController(ROBOT_IP, COLORS, COUNTS)
        
        # 运行LED序列
        controller.run_led_sequence(CYCLES, FREQUENCY)
        
    except rospy.ROSException as e:
        print(f"ROS Error: {e}")
        print("请确保:")
        print("1. 机器人IP地址正确")
        print("2. 机器人的ROS Master正在运行")
        print("3. 网络连接正常")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
