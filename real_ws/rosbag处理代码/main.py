import glob
import os
import argparse
import sys

def mkdir(path):
    if not os.path.exists(path):
        os.mkdir(path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="This is a code for bag2video."
    )
    parser.add_argument('--data_root', type=str, default=os.path.join('.', 'test', 'data'), help='path to the root of data')
    parser.add_argument('--out_root', type=str, default=os.path.join('.', 'test', 'save'), help='path to save the output videos')
    parser.add_argument('--fps', type=int, default=25, help='frames per second for the output videos')
    args = parser.parse_args()

    # 查找 bag 文件
    bagList = glob.glob(os.path.join(args.data_root, "*.bag"))
    
    # 如果未找到 bag 文件，则给出提示并退出
    if not bagList:
        print(f"No bag files found in the directory: {args.data_root}")
        sys.exit(1)
    
    # 创建输出文件夹
    mkdir(args.out_root)
    
    # 转换 bag 文件为视频
    for bagPath in bagList:
        baseName = os.path.basename(bagPath).split(".")[0]
        outPath = os.path.join(args.out_root, baseName + ".mp4")
        os.system("python rosbag2video.py -o {outName} --fps {fps} {bagPath}".format(
            outName=outPath, fps=args.fps, bagPath=bagPath
        ))
        print(f"Converted {bagPath} to {outPath}")
