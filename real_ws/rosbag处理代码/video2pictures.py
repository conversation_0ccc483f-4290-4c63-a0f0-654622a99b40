import cv2
import os

def video_to_frames(video_path, output_folder, video_id):
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    
    frame_count = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        # 保存帧图像，文件名包含视频ID和帧数
        frame_filename = os.path.join(output_folder, f"{video_id}_frame_{frame_count:04d}.png")
        cv2.imwrite(frame_filename, frame)
        frame_count += 1
    
    # 释放视频捕获对象
    cap.release()
    print(f"视频 {video_id} 共保存了 {frame_count} 帧图像到 {output_folder}")

def process_videos_in_folder(videos_folder, output_folder):
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 遍历文件夹中的所有文件
    for filename in os.listdir(videos_folder):
        if filename.endswith(".mp4") or filename.endswith(".avi") or filename.endswith(".mkv"):  # 可以根据需要添加其他视频格式
            video_path = os.path.join(videos_folder, filename)
            video_id = os.path.splitext(filename)[0]
            video_to_frames(video_path, output_folder, video_id)

# 使用示例
videos_folder = '/home/<USER>/BeeSwarm/BeeSwarm/src/real_ws/rosbag处理代码/20250108'
pictures_folder = '/home/<USER>/BeeSwarm/BeeSwarm/src/real_ws/rosbag处理代码/20250108'

process_videos_in_folder(videos_folder, pictures_folder)

