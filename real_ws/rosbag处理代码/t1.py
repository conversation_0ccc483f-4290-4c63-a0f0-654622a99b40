import cv2
import os

def extract_frames(video_path, output_folder):
    # 创建输出文件夹，如果文件夹不存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 打开视频文件
    video = cv2.VideoCapture(video_path)
    success, frame = video.read()
    count = 0

    while success:
        # 将帧保存为JPEG文件
        frame_filename = os.path.join(output_folder, f"frame{count:05d}.jpg")
        cv2.imwrite(frame_filename, frame)
        success, frame = video.read()
        count += 1

    # 释放视频捕获对象
    video.release()

if __name__ == "__main__":
    video_path = '2024-07-16-15-42-13_cam1_image_raw.mp4'  # 替换为你的视频文件路径
    output_folder = 'r1tupian'  # 替换为保存图像帧的文件夹路径
    extract_frames(video_path, output_folder)

