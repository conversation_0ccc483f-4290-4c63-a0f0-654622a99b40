#!/usr/bin/env python3
"""
通过TCP Socket直接控制机器人LED的脚本
绕过MQTT，使用直接的socket连接
"""

import socket
import json
import time
import struct

class SocketLEDController:
    def __init__(self, robot_ip, robot_port=23333, colors=None, counts=None):
        """
        初始化Socket LED控制器
        
        Args:
            robot_ip: 机器人的IP地址
            robot_port: 机器人socket服务端口
            colors: 颜色列表 (例如: [0xFF0000, 0x000000])
            counts: 每种颜色的显示次数 (例如: [12, 6])
        """
        self.robot_ip = robot_ip
        self.robot_port = robot_port
        self.colors = colors or [0xFF0000, 0x000000]
        self.counts = counts or [12, 6]
        self.current_color_idx = 0
        self.current_count = 0
        self.socket = None
        
        # 建立连接
        self.connect()

    def connect(self):
        """建立与机器人的socket连接"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)  # 10秒超时
            self.socket.connect((self.robot_ip, self.robot_port))
            print(f"✓ Connected to robot at {self.robot_ip}:{self.robot_port}")
            return True
        except socket.error as e:
            print(f"✗ Failed to connect to {self.robot_ip}:{self.robot_port}")
            print(f"Error: {e}")
            return False

    def disconnect(self):
        """断开socket连接"""
        if self.socket:
            self.socket.close()
            self.socket = None
            print("Disconnected from robot")

    def send_json_command(self, cmd_data):
        """
        发送JSON格式的命令到机器人
        
        Args:
            cmd_data: 命令数据字典
        """
        try:
            # 将命令转换为JSON字符串
            json_str = json.dumps(cmd_data)
            
            # 发送数据长度（4字节）+ JSON数据
            data_length = len(json_str.encode('utf-8'))
            length_bytes = struct.pack('!I', data_length)
            
            self.socket.send(length_bytes)
            self.socket.send(json_str.encode('utf-8'))
            
            print(f"✓ Sent command: {cmd_data['cmd_type']}")
            return True
            
        except socket.error as e:
            print(f"✗ Failed to send command: {e}")
            return False

    def send_raw_command(self, cmd_string):
        """
        发送原始字符串命令到机器人
        
        Args:
            cmd_string: 命令字符串
        """
        try:
            self.socket.send(cmd_string.encode('utf-8'))
            print(f"✓ Sent raw command: {cmd_string}")
            return True
        except socket.error as e:
            print(f"✗ Failed to send raw command: {e}")
            return False

    def send_led_command(self, cmd_type, color, num_leds):
        """
        发送LED控制命令到机器人
        
        Args:
            cmd_type: 'ledup' 或 'leddown'
            color: RGB颜色值
            num_leds: LED数量
        """
        # 构造命令消息（与MQTT版本相同的格式）
        cmd_msg = {
            "cmd_type": cmd_type,
            "args_length": 6,
            "args": {
                "0": color,
                "1": num_leds,
                "2": color,
                "3": num_leds,
                "4": color,
                "5": num_leds,
            }
        }
        
        return self.send_json_command(cmd_msg)

    def send_simple_led_command(self, color):
        """
        发送简化的LED命令
        
        Args:
            color: RGB颜色值
        """
        # 尝试简化的命令格式
        cmd_string = f"LED:{hex(color)}\n"
        return self.send_raw_command(cmd_string)

    def update_color(self):
        """更新当前颜色"""
        self.current_count += 1
        
        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

    def publish_led_color(self, color):
        """
        发布LED颜色到机器人
        
        Args:
            color: RGB颜色值
        """
        # 尝试发送标准格式命令
        success1 = self.send_led_command("ledup", color, 14)
        success2 = self.send_led_command("leddown", color, 30)
        
        # 如果标准格式失败，尝试简化格式
        if not (success1 and success2):
            self.send_simple_led_command(color)

    def run_led_sequence(self, total_cycles=5, frequency=12):
        """
        运行LED闪烁序列
        
        Args:
            total_cycles: 总循环次数
            frequency: 频率 (Hz)
        """
        if not self.socket:
            print("✗ No connection to robot")
            return
            
        interval = 1.0 / frequency
        total_count = total_cycles * sum(self.counts) - 1
        
        print(f"Starting LED sequence: {total_cycles} cycles at {frequency}Hz")
        print(f"Colors: {[hex(c) for c in self.colors]}")
        print(f"Counts: {self.counts}")
        
        try:
            for count in range(total_count, -1, -1):
                # 更新并发布当前颜色
                current_color = self.colors[self.current_color_idx]
                self.publish_led_color(current_color)
                self.update_color()
                
                print(f"Count: {count}, Color: {hex(current_color)}")
                time.sleep(interval)
            
            # 序列结束后关闭LED
            self.publish_led_color(0x000000)
            print("LED sequence completed, lights turned off")
            
        except KeyboardInterrupt:
            print("\nSequence interrupted by user")
        finally:
            self.disconnect()


class BinaryProtocolLEDController:
    """使用二进制协议的LED控制器（基于机器人的串口协议）"""
    
    def __init__(self, robot_ip, robot_port=23334):
        self.robot_ip = robot_ip
        self.robot_port = robot_port
        self.socket = None
        self.connect()
    
    def connect(self):
        """建立连接"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.robot_ip, self.robot_port))
            print(f"✓ Binary protocol connected to {self.robot_ip}:{self.robot_port}")
            return True
        except socket.error as e:
            print(f"✗ Binary protocol connection failed: {e}")
            return False
    
    def send_binary_led_command(self, cmd, color_data):
        """
        发送二进制LED命令（基于机器人的串口协议）
        
        Args:
            cmd: 0x70 (上灯带) 或 0x71 (下灯带)
            color_data: 颜色数据列表
        """
        try:
            # 构造二进制数据包
            # 格式: FE AA cmd length data checksum1 checksum2
            packet = bytearray()
            packet.append(0xFE)  # 帧头
            packet.append(0xAA)  # 地址（主机）
            packet.append(cmd)   # 功能码
            
            # 数据长度和数据
            data_bytes = bytearray()
            for color in color_data:
                data_bytes.extend(struct.pack('<I', color))  # 小端序uint32
            
            packet.append(len(data_bytes))  # 长度
            packet.extend(data_bytes)       # 数据
            
            # 计算校验和
            sum1 = sum(packet) & 0xFF
            sum2 = (sum(packet) + sum1) & 0xFF
            
            packet.append(sum1)
            packet.append(sum2)
            
            # 发送数据包
            self.socket.send(packet)
            print(f"✓ Sent binary LED command: 0x{cmd:02X}")
            return True
            
        except socket.error as e:
            print(f"✗ Failed to send binary command: {e}")
            return False
    
    def set_led_color(self, color):
        """设置LED颜色"""
        if not self.socket:
            return False
            
        # 转换颜色格式：RGB -> BRG + 数量
        r = (color >> 16) & 0xFF
        g = (color >> 8) & 0xFF
        b = color & 0xFF
        
        # 上灯带数据（14个LED）
        up_color = (14 << 24) | (g << 16) | (r << 8) | b
        self.send_binary_led_command(0x70, [up_color])
        
        # 下灯带数据（30个LED）
        down_color = (30 << 24) | (g << 16) | (r << 8) | b
        self.send_binary_led_command(0x71, [down_color])
        
        return True


def main():
    """主函数"""
    # 配置参数 - 请根据你的机器人实际情况修改
    ROBOT_IP = "*************"  # 替换为你的机器人IP地址
    
    COLORS = [0xFF0000, 0x000000]  # 红色和黑色（关闭）
    COUNTS = [12, 6]  # 红色12次，黑色6次
    CYCLES = 5  # 总共5个循环
    FREQUENCY = 12  # 12Hz频率
    
    print("尝试Socket LED控制...")
    print(f"目标机器人: {ROBOT_IP}")
    
    # 尝试不同的端口和协议
    ports_to_try = [23333, 23334, 8080, 5000, 9999]
    
    for port in ports_to_try:
        print(f"\n尝试端口 {port}...")
        
        try:
            controller = SocketLEDController(ROBOT_IP, port, COLORS, COUNTS)
            if controller.socket:
                controller.run_led_sequence(CYCLES, FREQUENCY)
                break
        except Exception as e:
            print(f"端口 {port} 失败: {e}")
            continue
    else:
        print("\n所有标准端口都失败，尝试二进制协议...")
        try:
            binary_controller = BinaryProtocolLEDController(ROBOT_IP)
            if binary_controller.socket:
                # 简单的红色闪烁测试
                for i in range(10):
                    binary_controller.set_led_color(0xFF0000)  # 红色
                    time.sleep(0.5)
                    binary_controller.set_led_color(0x000000)  # 关闭
                    time.sleep(0.5)
        except Exception as e:
            print(f"二进制协议也失败: {e}")
            print("\n建议:")
            print("1. 检查机器人IP地址是否正确")
            print("2. 确认机器人的socket服务端口")
            print("3. 检查网络连接和防火墙设置")
            print("4. 或者使用ROS话题版本 (direct_ros_led_ctrl.py)")


if __name__ == "__main__":
    main()
