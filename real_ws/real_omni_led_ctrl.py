import json
import os
import time
import rospy
from std_msgs.msg import Float32MultiArray
from paho.mqtt import client as mqtt_client

# 设置LED灯的颜色
def LedSetColor(color_list, color):
    color_list.clear()
    color_list.append(((color >> 16) & 0xff) / 256)  # 红色分量
    color_list.append(((color >> 8) & 0xff) / 256)   # 绿色分量
    color_list.append(((color) & 0xff) / 256)        # 蓝色分量
    color_list.append(1)                             # 透明度

# LED控制器类
class LEDController:
    def __init__(self, colors, counts, color_list, mqtt_client, entity_id):
        self.colors = colors                  # 颜色列表
        self.counts = counts                  # 每种颜色的显示次数
        self.current_color_idx = 0            # 当前颜色的索引
        self.current_count = 0                # 当前颜色已显示的次数
        self.color_list = color_list          # 存放当前颜色的列表
        self.mqtt_client = mqtt_client        # MQTT客户端
        self.entity_id = entity_id            # 实体ID

    def update_color(self):
        # 更新当前颜色的显示次数
        self.current_count += 1

        # 如果达到计数上限，切换到下一种颜色
        if self.current_count >= self.counts[self.current_color_idx]:
            self.current_color_idx = (self.current_color_idx + 1) % len(self.colors)
            self.current_count = 0

        # 设置当前颜色
        LedSetColor(self.color_list, self.colors[self.current_color_idx])

    def publish(self):
        # 发布当前颜色到MQTT
        self.set_ledup(self.entity_id, self.colors[self.current_color_idx])
        self.set_leddown(self.entity_id, self.colors[self.current_color_idx])
        # print(f"LED color updated to {self.colors[self.current_color_idx]}")

    def set_ledup(self, entity_id, led_colors):
        json_msg = {
            "cmd_type": "ledup",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 14,
                "2": led_colors,
                "3": 14,
                "4": led_colors,
                "5": 14,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )

    def set_leddown(self, entity_id, led_colors):
        json_msg = {
            "cmd_type": "leddown",
            "args_length": 6,
            "args": {
                "0": led_colors,
                "1": 30,
                "2": led_colors,
                "3": 30,
                "4": led_colors,
                "5": 30,
            },
        }
        json_str = json.dumps(json_msg)
        self.mqtt_client.publish(
            f"/VSWARM{entity_id}_robot/cmd", json_str.encode("utf-8")
        )


class MqttClientThread:
    def __init__(self, broker, port, keepalive, client_id):
        self.broker = broker  # MQTT代理服务器地址
        self.port = port
        self.keepalive = keepalive
        self.reconnect_interval = 1
        self.client_id = client_id
        self.client = self.connect_mqtt()

    def connect_mqtt(self):
        """连接MQTT代理服务器"""

        def on_connect(client, userdata, flags, rc):
            """连接回调函数"""
            if rc == 0:
                print("Connected to MQTT OK!")
            else:
                print(f"Failed to connect, return code {rc}")
        client = mqtt_client.Client(self.client_id)
        client.on_connect = on_connect
        client.connect(self.broker, self.port, self.keepalive)
        return client

    def start_up_mqtt_thread(self):
        """初始化并启动MQTT线程"""
        try:
            broker = os.environ.get("REMOTE_SERVER", self.broker)
            net_status = -1
            while net_status != 0:
                net_status = os.system(f"ping -c 4 {broker}")
                time.sleep(2)
            self.client.loop_start()
        except Exception as e:
            print(f"Error starting MQTT thread: {e}")

    def run(self):
        """启动MQTT客户端"""
        while True:
            try:
                self.client.loop_forever()
            except Exception as e:
                print(f"Error in MQTT loop: {e}")
                time.sleep(self.reconnect_interval)

    def publish(self, topic, msg):
        """发布消息到指定主题"""
        result = self.client.publish(topic, msg)
        status = result[0]
        if status == 0:
            pass
        else:
            print(f"Failed to send message to topic {topic}")

if __name__ == "__main__":
    # 初始化ROS节点
    rospy.init_node("user_led_ctrl")

    # MQTT配置
    broker_ip = "*********"
    port = 1883
    keepalive = 60
    client_id = "LEDController"
    mqtt_client_instance = MqttClientThread(broker_ip, port, keepalive, client_id)

    # 启动MQTT线程
    mqtt_client_instance.start_up_mqtt_thread()

    # 初始化颜色和闪烁标志变量
    color_list = []

    # 配置LED控制器
    led_controller = LEDController(
        colors=[0xFF0000, 0x000000], 
        counts=[12, 6], 
        color_list=color_list, 
        mqtt_client=mqtt_client_instance, 
        entity_id=4     #根据被控对象，设置不同的ID
    )

    # 设置循环频率为12Hz
    rate = rospy.Rate(12)
    # 设置循环次数
    count = 5 * (12 + 6) - 1    # 5次循环周期*（155行中的counts之和）
    # 进入循环，直到ROS节点关闭
    while not rospy.is_shutdown() and count>0:
        # 更新并发布LED颜色
        led_controller.update_color()
        led_controller.publish()
        count = count - 1
        # print(f"count is {count}")

        # 等待下一次循环
        rate.sleep()
    
    # 灯语结束后，设置灯灭
    led_controller = LEDController(
        colors=[0x000000, 0x000000], 
        counts=[6, 6], 
        color_list=color_list, 
        mqtt_client=mqtt_client_instance, 
        entity_id=210     #根据被控对象，设置不同的ID
    )
    # 更新并发布LED颜色
    led_controller.update_color()
    led_controller.publish()
